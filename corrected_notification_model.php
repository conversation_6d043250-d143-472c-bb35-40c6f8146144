<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'message',
        'admin_id',
        'student_ids',
        'student_classes',
        'employee_ids',
        'employee_companies',
        'recipient_type',
        'has_attachment',
    ];

    protected $casts = [
        'student_ids' => 'array',
        'student_classes' => 'array',
        'employee_ids' => 'array',
        'employee_companies' => 'array',
        'has_attachment' => 'boolean',
    ];

    /**
     * Get the admin user who sent this notification
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Alias for admin relationship to match API expectations
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Get students who should receive this notification
     */
    public function students()
    {
        return $this->belongsToMany(Student::class, 'notification_reads', 'notification_id', 'student_id');
    }

    /**
     * Get employees who should receive this notification
     */
    public function employees()
    {
        return $this->belongsToMany(Emp::class, 'employee_notification_reads', 'notification_id', 'employee_id');
    }

    /**
     * Scope for notifications that should be visible to a specific user
     */
    public function scopeForUser($query, User $user)
    {
        return $query->where(function ($q) use ($user) {
            $q->where('recipient_type', 'all')
              ->orWhere('recipient_type', $user->user_type . 's')
              ->orWhere(function ($subQuery) use ($user) {
                  if ($user->user_type === 'student') {
                      $subQuery->where('recipient_type', 'specific')
                               ->whereJsonContains('student_ids', $user->id);
                  } elseif ($user->user_type === 'employee') {
                      $subQuery->where('recipient_type', 'specific')
                               ->whereJsonContains('employee_ids', $user->id);
                  }
              });
        });
    }

    /**
     * Check if notification has attachment
     */
    public function hasAttachment()
    {
        return $this->has_attachment;
    }

    /**
     * Get attachments for this notification (database relationship)
     */
    public function attachments()
    {
        return $this->hasMany(NotificationAttachment::class, 'notification_id');
    }

    /**
     * Scope to include attachments with the notification
     */
    public function scopeWithAttachments($query)
    {
        return $query->with('attachments');
    }

    /**
     * Get attachment URL (you may need to implement this based on your file storage)
     */
    public function getAttachmentUrlAttribute()
    {
        if ($this->has_attachment) {
            // Return the attachment URL based on your file storage logic
            return asset('storage/notifications/' . $this->id . '/attachment');
        }
        return null;
    }

    /**
     * Get attachment name (you may need to implement this based on your file storage)
     */
    public function getAttachmentNameAttribute()
    {
        if ($this->has_attachment) {
            // Return the attachment name based on your file storage logic
            return 'attachment_' . $this->id;
        }
        return null;
    }
}
