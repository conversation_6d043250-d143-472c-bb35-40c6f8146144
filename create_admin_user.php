<?php

// Simple script to create admin user
// Copy this file to C:\laragon\www\notysend\ and run it in browser

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

// Database configuration
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '127.0.0.1',
    'database' => 'notysend',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Check if users table exists and what columns it has
    $columns = Capsule::select("DESCRIBE users");
    echo "<h2>Users table structure:</h2>";
    echo "<pre>";
    foreach ($columns as $column) {
        echo $column->Field . " - " . $column->Type . "\n";
    }
    echo "</pre>";

    // Check existing users
    $users = Capsule::table('users')->get();
    echo "<h2>Existing users:</h2>";
    echo "<pre>";
    foreach ($users as $user) {
        echo "ID: " . $user->id . ", Name: " . $user->name . ", Email: " . $user->email . "\n";
    }
    echo "</pre>";

    // Try to create admin user
    $adminExists = Capsule::table('users')->where('email', '<EMAIL>')->first();
    
    if (!$adminExists) {
        $userData = [
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        // Add user_type if column exists
        $hasUserType = false;
        foreach ($columns as $column) {
            if ($column->Field === 'user_type') {
                $hasUserType = true;
                break;
            }
        }

        if ($hasUserType) {
            $userData['user_type'] = 'admin';
        }

        $userId = Capsule::table('users')->insertGetId($userData);
        echo "<h2>✅ Admin user created successfully!</h2>";
        echo "<p>User ID: " . $userId . "</p>";
        echo "<p>Email: <EMAIL></p>";
        echo "<p>Password: admin123</p>";
    } else {
        echo "<h2>ℹ️ Admin user already exists!</h2>";
        echo "<p>Email: <EMAIL></p>";
        echo "<p>Password: admin123</p>";
    }

} catch (Exception $e) {
    echo "<h2>❌ Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
