<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\StudentController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\NotificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/auth/login', [AuthController::class, 'login']);
Route::post('/auth/register', [AuthController::class, 'register']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/user', [AuthController::class, 'user']);
    
    // Admin routes
    Route::prefix('admin')->group(function () {
        // Students management
        Route::apiResource('students', StudentController::class);
        Route::get('/students/search', [StudentController::class, 'search']);
        Route::get('/students/statistics', [StudentController::class, 'statistics']);
        
        // Notifications management
        Route::apiResource('notifications', NotificationController::class);
        Route::post('/notifications/send', [NotificationController::class, 'sendNotification']);
        Route::post('/notifications/{id}/mark-read', [NotificationController::class, 'markAsRead']);
        Route::delete('/notifications/bulk-delete', [NotificationController::class, 'bulkDelete']);
    });
    
    // General notifications (for all users)
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'getUserNotifications']);
        Route::post('/{id}/read', [NotificationController::class, 'markAsRead']);
        Route::get('/unread-count', [NotificationController::class, 'getUnreadCount']);
    });
});

// Test route to check if API is working
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working!',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});

// Health check route
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'database' => 'connected',
        'timestamp' => now()
    ]);
});

// Get all students (public route for testing)
Route::get('/students/all', function () {
    try {
        $students = \App\Models\Student::all();
        return response()->json([
            'success' => true,
            'message' => 'Students retrieved successfully',
            'students' => $students,
            'total' => $students->count()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to retrieve students',
            'error' => $e->getMessage()
        ], 500);
    }
});

// CORS preflight handling
Route::options('{any}', function () {
    return response('', 200)
        ->header('Access-Control-Allow-Origin', '*')
        ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
})->where('any', '.*');
