# Laravel Setup Commands for NotiSend API

# 1. Create Laravel project
composer create-project laravel/laravel notysend
cd notysend

# 2. Install required packages
composer require laravel/sanctum
composer require fruitcake/laravel-cors

# 3. Publish Sanctum configuration
php artisan vendor:publish --provider="<PERSON><PERSON>\Sanctum\SanctumServiceProvider"

# 4. Create models and migrations
php artisan make:model Notification -m
php artisan make:model UserNotification -m

# 5. Create controllers
php artisan make:controller Api/AuthController
php artisan make:controller Api/NotificationController
php artisan make:controller Api/UserController

# 6. Create seeders
php artisan make:seeder AdminSeeder
php artisan make:seeder NotificationSeeder

# 7. Generate application key
php artisan key:generate

# 8. Run migrations
php artisan migrate

# 9. Run seeders
php artisan db:seed --class=AdminSeeder

# 10. Start server
php artisan serve --host=0.0.0.0 --port=8000
