<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'user_type' => 'admin',
                'phone' => '+1234567890',
                'department' => 'Administration',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create Teacher User
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Teacher User',
                'email' => '<EMAIL>',
                'password' => Hash::make('teacher123'),
                'user_type' => 'teacher',
                'phone' => '+1234567891',
                'department' => 'Computer Science',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create Student User
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Student User',
                'email' => '<EMAIL>',
                'password' => Hash::make('student123'),
                'user_type' => 'student',
                'phone' => '+1234567892',
                'department' => 'Computer Science',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        echo "✅ Admin, Teacher, and Student users created successfully!\n";
        echo "Admin: <EMAIL> / admin123\n";
        echo "Teacher: <EMAIL> / teacher123\n";
        echo "Student: <EMAIL> / student123\n";
    }
}
