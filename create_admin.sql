-- SQL script to create admin user
-- Run this in phpMyAdmin or MySQL command line

USE notysend;

-- Check current users table structure
DESCRIBE users;

-- Check existing users
SELECT id, name, email FROM users;

-- Insert admin user (adjust columns based on your table structure)
INSERT INTO users (name, email, password, created_at, updated_at) 
VALUES (
    'Admin User', 
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 'password'
    NOW(), 
    NOW()
) 
ON DUPLICATE KEY UPDATE 
    password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

-- If your table has user_type column, use this instead:
-- INSERT INTO users (name, email, password, user_type, created_at, updated_at) 
-- VALUES (
--     'Admin User', 
--     '<EMAIL>', 
--     '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
--     'admin',
--     NOW(), 
--     NOW()
-- ) 
-- ON DUPLICATE KEY UPDATE 
--     password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

-- Verify the user was created
SELECT id, name, email FROM users WHERE email = '<EMAIL>';
