import 'dart:convert';

class NotificationModel {
  final int id;
  final String title;
  final String message;
  final String type; // 'general', 'urgent', 'announcement', 'reminder'
  final int senderId;
  final String senderName;
  final String senderType;
  final String recipientType; // 'students', 'teachers', 'all', 'specific'
  final List<int>? specificRecipients;
  final String? attachmentUrl;
  final String? attachmentName;
  final bool isRead;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? readAt;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.senderId,
    required this.senderName,
    required this.senderType,
    required this.recipientType,
    this.specificRecipients,
    this.attachmentUrl,
    this.attachmentName,
    this.isRead = false,
    required this.createdAt,
    required this.updatedAt,
    this.readAt,
  });

  // Factory constructor from Map
  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id']?.toInt() ?? 0,
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      type: map['type'] ?? 'general',
      senderId: map['sender_id']?.toInt() ?? 0,
      senderName: map['sender_name'] ?? map['sender']?['name'] ?? '',
      senderType: map['sender_type'] ?? map['sender']?['user_type'] ?? '',
      recipientType: map['recipient_type'] ?? 'all',
      specificRecipients: map['specific_recipients'] != null 
          ? List<int>.from(map['specific_recipients'])
          : null,
      attachmentUrl: map['attachment_url'],
      attachmentName: map['attachment_name'],
      isRead: map['is_read'] ?? false,
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
      readAt: map['read_at'] != null ? DateTime.parse(map['read_at']) : null,
    );
  }

  // Factory constructor from JSON string
  factory NotificationModel.fromJson(String source) => 
      NotificationModel.fromMap(json.decode(source));

  // Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'sender_id': senderId,
      'sender_name': senderName,
      'sender_type': senderType,
      'recipient_type': recipientType,
      'specific_recipients': specificRecipients,
      'attachment_url': attachmentUrl,
      'attachment_name': attachmentName,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
    };
  }

  // Convert to JSON string
  String toJson() => json.encode(toMap());

  // Copy with method
  NotificationModel copyWith({
    int? id,
    String? title,
    String? message,
    String? type,
    int? senderId,
    String? senderName,
    String? senderType,
    String? recipientType,
    List<int>? specificRecipients,
    String? attachmentUrl,
    String? attachmentName,
    bool? isRead,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? readAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderType: senderType ?? this.senderType,
      recipientType: recipientType ?? this.recipientType,
      specificRecipients: specificRecipients ?? this.specificRecipients,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
      attachmentName: attachmentName ?? this.attachmentName,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      readAt: readAt ?? this.readAt,
    );
  }

  // Get notification type in Arabic
  String get typeArabic {
    switch (type) {
      case 'general':
        return 'عام';
      case 'urgent':
        return 'عاجل';
      case 'announcement':
        return 'إعلان';
      case 'reminder':
        return 'تذكير';
      default:
        return 'إشعار';
    }
  }

  // Get recipient type in Arabic
  String get recipientTypeArabic {
    switch (recipientType) {
      case 'students':
        return 'الطلاب';
      case 'teachers':
        return 'المعلمين';
      case 'all':
        return 'الجميع';
      case 'specific':
        return 'محدد';
      default:
        return 'غير محدد';
    }
  }

  // Get sender type in Arabic
  String get senderTypeArabic {
    switch (senderType) {
      case 'admin':
        return 'مدير';
      case 'teacher':
        return 'معلم';
      case 'student':
        return 'طالب';
      default:
        return 'مستخدم';
    }
  }

  // Check if notification has attachment
  bool get hasAttachment => attachmentUrl != null && attachmentUrl!.isNotEmpty;

  // Check if notification is urgent
  bool get isUrgent => type == 'urgent';

  // Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  // Get formatted date
  String get formattedDate {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  // Get formatted time
  String get formattedTime {
    final hour = createdAt.hour > 12 ? createdAt.hour - 12 : createdAt.hour;
    final period = createdAt.hour >= 12 ? 'م' : 'ص';
    return '${hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')} $period';
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, message: $message, type: $type, senderId: $senderId, senderName: $senderName, senderType: $senderType, recipientType: $recipientType, specificRecipients: $specificRecipients, attachmentUrl: $attachmentUrl, attachmentName: $attachmentName, isRead: $isRead, createdAt: $createdAt, updatedAt: $updatedAt, readAt: $readAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is NotificationModel &&
      other.id == id &&
      other.title == title &&
      other.message == message &&
      other.type == type &&
      other.senderId == senderId &&
      other.senderName == senderName &&
      other.senderType == senderType &&
      other.recipientType == recipientType &&
      other.attachmentUrl == attachmentUrl &&
      other.attachmentName == attachmentName &&
      other.isRead == isRead &&
      other.createdAt == createdAt &&
      other.updatedAt == updatedAt &&
      other.readAt == readAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      title.hashCode ^
      message.hashCode ^
      type.hashCode ^
      senderId.hashCode ^
      senderName.hashCode ^
      senderType.hashCode ^
      recipientType.hashCode ^
      attachmentUrl.hashCode ^
      attachmentName.hashCode ^
      isRead.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      readAt.hashCode;
  }
}
