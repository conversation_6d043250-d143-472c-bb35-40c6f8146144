<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Student;
use App\Models\Notification;
use App\Models\User;

class DashboardController extends Controller
{
    public function index()
    {
        $totalStudents = Student::count();
        $totalNotifications = Notification::count();
        $totalAdminUsers = User::count();
        $recentNotifications = Notification::with('sender')
                                          ->latest()
                                          ->take(5)
                                          ->get();

        return view('admin.dashboard', compact('totalStudents', 'totalNotifications', 'totalAdminUsers', 'recentNotifications'));
    }
}
