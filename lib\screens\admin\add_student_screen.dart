import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/student_provider.dart';
import '../../models/student_model.dart';
import '../../utils/app_theme.dart';

class AddStudentScreen extends StatefulWidget {
  const AddStudentScreen({super.key});

  @override
  State<AddStudentScreen> createState() => _AddStudentScreenState();
}

class _AddStudentScreenState extends State<AddStudentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _nationalityController = TextEditingController();
  final _phoneController = TextEditingController();
  final _classController = TextEditingController();
  final _parentPhoneController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _obscurePassword = true;

  // Predefined options
  final List<String> _nationalities = [
    'اللبنانية',
    'السورية',
    'الفلسطينية',
    'المصرية',
    'الأردنية',
    'العراقية',
    'أخرى'
  ];

  final List<String> _classes = [
    'BT1 كهرباء A1 (2024-2025)',
    'BT2 كهرباء A1 (2024-2025)',
    'BT3 كهرباء A1 (2024-2025)',
    'TS1 رقابة F1 (2024-2025)',
    'TS2 رقابة F1 (2024-2025)',
    'TS3 رقابة F1 (2024-2025)',
    'BP1 ميكانيك A1 (2024-2025)',
    'BP2 ميكانيك A1 (2024-2025)',
    'BP3 ميكانيك A1 (2024-2025)',
  ];

  String? _selectedNationality;
  String? _selectedClass;

  @override
  void initState() {
    super.initState();
    _passwordController.text = 'student123'; // Default password
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _nationalityController.dispose();
    _phoneController.dispose();
    _classController.dispose();
    _parentPhoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _addStudent() async {
    if (!_formKey.currentState!.validate()) return;

    final studentProvider = Provider.of<StudentProvider>(context, listen: false);

    final student = Student(
      fullName: _fullNameController.text.trim(),
      nationality: _selectedNationality ?? _nationalityController.text.trim(),
      phoneNumber: _phoneController.text.trim(),
      studentClass: _selectedClass ?? _classController.text.trim(),
      parentPhoneNumber: _parentPhoneController.text.trim(),
      password: _passwordController.text.trim(),
    );

    final success = await studentProvider.addStudent(student);

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إضافة الطالب بنجاح!'),
          backgroundColor: AppTheme.successColor,
        ),
      );
      Navigator.of(context).pop();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(studentProvider.error ?? 'فشل في إضافة الطالب'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('إضافة طالب جديد'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Header
                    const Text(
                      'بيانات الطالب',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryGreen,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: 30),
                    
                    // Full Name
                    TextFormField(
                      controller: _fullNameController,
                      decoration: const InputDecoration(
                        labelText: 'الاسم الكامل *',
                        prefixIcon: Icon(Icons.person),
                        hintText: 'أدخل الاسم الكامل للطالب',
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال الاسم الكامل';
                        }
                        if (value.trim().length < 3) {
                          return 'الاسم يجب أن يكون 3 أحرف على الأقل';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Nationality Dropdown
                    DropdownButtonFormField<String>(
                      value: _selectedNationality,
                      decoration: const InputDecoration(
                        labelText: 'الجنسية *',
                        prefixIcon: Icon(Icons.flag),
                      ),
                      items: _nationalities.map((nationality) {
                        return DropdownMenuItem(
                          value: nationality,
                          child: Text(nationality),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedNationality = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى اختيار الجنسية';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Phone Number
                    TextFormField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف *',
                        prefixIcon: Icon(Icons.phone),
                        hintText: '70123456',
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال رقم الهاتف';
                        }
                        if (value.trim().length < 8) {
                          return 'رقم الهاتف يجب أن يكون 8 أرقام على الأقل';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Class Dropdown
                    DropdownButtonFormField<String>(
                      value: _selectedClass,
                      decoration: const InputDecoration(
                        labelText: 'الصف *',
                        prefixIcon: Icon(Icons.school),
                      ),
                      items: _classes.map((className) {
                        return DropdownMenuItem(
                          value: className,
                          child: Text(
                            className,
                            style: const TextStyle(fontSize: 12),
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedClass = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى اختيار الصف';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Parent Phone Number
                    TextFormField(
                      controller: _parentPhoneController,
                      keyboardType: TextInputType.phone,
                      decoration: const InputDecoration(
                        labelText: 'رقم هاتف ولي الأمر *',
                        prefixIcon: Icon(Icons.contact_phone),
                        hintText: '81123456',
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال رقم هاتف ولي الأمر';
                        }
                        if (value.trim().length < 8) {
                          return 'رقم الهاتف يجب أن يكون 8 أرقام على الأقل';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Password
                    TextFormField(
                      controller: _passwordController,
                      obscureText: _obscurePassword,
                      decoration: InputDecoration(
                        labelText: 'كلمة المرور *',
                        prefixIcon: const Icon(Icons.lock),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword ? Icons.visibility : Icons.visibility_off,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال كلمة المرور';
                        }
                        if (value.trim().length < 6) {
                          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 40),

                    // Add Button
                    Consumer<StudentProvider>(
                      builder: (context, studentProvider, child) {
                        return ElevatedButton(
                          onPressed: studentProvider.isLoading ? null : _addStudent,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            backgroundColor: AppTheme.primaryGreen,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: studentProvider.isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text(
                                  'إضافة الطالب',
                                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
