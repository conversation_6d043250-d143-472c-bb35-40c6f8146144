# إدارة الطلاب في تطبيق NotiSend

## 🎓 الميزات الجديدة المضافة

### ✅ **إضافة الطلاب**
- صفحة إضافة طالب جديد مع جميع البيانات المطلوبة
- قوائم منسدلة للجنسية والصف
- التحقق من صحة البيانات المدخلة
- كلمة مرور افتراضية للطلاب

### ✅ **قائمة الطلاب**
- عرض جميع الطلاب في قائمة منظمة
- البحث في قائمة الطلاب
- حذف الطلاب مع تأكيد
- تحديث القائمة بالسحب للأسفل

### ✅ **لوحة تحكم المدير**
- أزرار سريعة لإدارة الطلاب
- إحصائيات الطلاب
- الوصول السريع لإضافة طالب جديد

## 📊 هيكل قاعدة البيانات

### جدول `students`
```sql
- id (bigint, primary key)
- full_name (varchar) - الاسم الكامل
- nationality (varchar) - الجنسية
- phone_number (varchar) - رقم الهاتف
- class (varchar) - الصف
- parent_phone_number (varchar) - رقم هاتف ولي الأمر
- password (varchar) - كلمة المرور (مشفرة)
- created_at (timestamp)
- updated_at (timestamp)
```

## 🚀 كيفية الاستخدام

### 1. **الوصول لإدارة الطلاب**
1. سجل دخول كمدير
2. في لوحة التحكم، اضغط على "إدارة الطلاب"
3. أو اضغط على "إضافة طالب جديد" للإضافة المباشرة

### 2. **إضافة طالب جديد**
1. املأ جميع الحقول المطلوبة:
   - الاسم الكامل
   - الجنسية (من القائمة المنسدلة)
   - رقم الهاتف
   - الصف (من القائمة المنسدلة)
   - رقم هاتف ولي الأمر
   - كلمة المرور (افتراضية: student123)
2. اضغط "إضافة الطالب"

### 3. **البحث في قائمة الطلاب**
- استخدم مربع البحث للبحث بالاسم أو رقم الهاتف أو الصف
- النتائج تظهر فورياً أثناء الكتابة

### 4. **حذف طالب**
1. في قائمة الطلاب، اضغط على النقاط الثلاث بجانب اسم الطالب
2. اختر "حذف"
3. أكد الحذف في النافذة المنبثقة

## 🔧 الإعدادات المطلوبة

### 1. **إعدادات API**
تأكد من تحديث `lib/utils/api_config.dart`:
```dart
static const String baseUrl = 'http://notysend.test'; // عنوان خادم Laravel
```

### 2. **Laravel Backend**
تأكد من وجود المسارات التالية في Laravel:
```php
// في routes/api.php
Route::prefix('admin')->group(function () {
    Route::get('/students', [StudentController::class, 'index']);
    Route::post('/students', [StudentController::class, 'store']);
    Route::put('/students/{id}', [StudentController::class, 'update']);
    Route::delete('/students/{id}', [StudentController::class, 'destroy']);
});
```

## 📱 الصفحات المضافة

### 1. **AddStudentScreen**
- `lib/screens/admin/add_student_screen.dart`
- نموذج إضافة طالب جديد

### 2. **StudentsListScreen**
- `lib/screens/admin/students_list_screen.dart`
- قائمة الطلاب مع البحث والحذف

### 3. **Student Model**
- `lib/models/student_model.dart`
- نموذج بيانات الطالب

### 4. **Student Provider**
- `lib/providers/student_provider.dart`
- إدارة حالة الطلاب والاتصال بالAPI

## 🎨 التصميم

- **الألوان**: تتماشى مع ألوان التطبيق الأساسية
- **الخط**: يدعم اللغة العربية
- **التخطيط**: متجاوب ومناسب للهواتف المحمولة
- **الأيقونات**: واضحة ومعبرة

## 🔐 الأمان

- **كلمات المرور**: مشفرة في قاعدة البيانات
- **التحقق**: من صحة البيانات قبل الإرسال
- **الصلاحيات**: فقط المدير يمكنه إدارة الطلاب

## 📝 ملاحظات مهمة

1. **كلمة المرور الافتراضية**: `student123`
2. **تنسيق رقم الهاتف**: 8 أرقام على الأقل
3. **الجنسيات المتاحة**: اللبنانية، السورية، الفلسطينية، المصرية، الأردنية، العراقية، أخرى
4. **الصفوف المتاحة**: مأخوذة من قاعدة البيانات الحالية

## 🚨 استكشاف الأخطاء

### خطأ الاتصال بالخادم
1. تأكد من تشغيل خادم Laravel
2. تحقق من عنوان API في `api_config.dart`
3. تأكد من وجود المسارات في Laravel

### عدم ظهور البيانات
1. تحقق من اتصال الإنترنت
2. تأكد من صحة استجابة API
3. راجع سجلات الأخطاء في Flutter

## 🔄 التحديثات المستقبلية

- [ ] تعديل بيانات الطالب
- [ ] تصدير قائمة الطلاب
- [ ] إضافة صور للطلاب
- [ ] إرسال إشعارات للطلاب المحددين
- [ ] إحصائيات مفصلة للطلاب
