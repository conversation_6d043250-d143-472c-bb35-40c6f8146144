<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Simple test route
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working!',
        'timestamp' => now(),
        'server' => 'Laravel ' . app()->version()
    ]);
});

// Health check
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'database' => 'connected',
        'timestamp' => now()
    ]);
});

// Simple students route (using direct database query)
Route::get('/students/all', function () {
    try {
        // Direct database query to avoid model issues
        $students = DB::table('students')->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Students retrieved successfully',
            'students' => $students,
            'total' => $students->count()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to retrieve students',
            'error' => $e->getMessage()
        ], 500);
    }
});

// Test database connection
Route::get('/test-db', function () {
    try {
        $studentsCount = DB::table('students')->count();
        $notificationsCount = DB::table('notifications')->count();
        
        return response()->json([
            'success' => true,
            'message' => 'Database connection successful',
            'data' => [
                'students_count' => $studentsCount,
                'notifications_count' => $notificationsCount
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ], 500);
    }
});

// Get Laravel info
Route::get('/info', function () {
    return response()->json([
        'laravel_version' => app()->version(),
        'php_version' => PHP_VERSION,
        'environment' => app()->environment(),
        'debug_mode' => config('app.debug'),
        'timezone' => config('app.timezone'),
        'url' => config('app.url')
    ]);
});

// CORS preflight handling
Route::options('{any}', function () {
    return response('', 200)
        ->header('Access-Control-Allow-Origin', '*')
        ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
})->where('any', '.*');
