class ApiConfig {
  // Base URL for your Laravel API
  static const String baseUrl = 'http://localhost:8000'; // Try localhost first
  
  // Alternative URLs for different environments
  static const String localUrl = 'http://localhost:8000';
  static const String laravelUrl = 'http://notysend.test';
  
  // API endpoints
  static const String loginEndpoint = '/api/auth/login';
  static const String registerEndpoint = '/api/auth/register';
  static const String logoutEndpoint = '/api/auth/logout';
  static const String userEndpoint = '/api/auth/user';
  
  // Students endpoints
  static const String studentsEndpoint = '/api/admin/students';
  static const String addStudentEndpoint = '/api/admin/students';
  static const String updateStudentEndpoint = '/api/admin/students'; // + /{id}
  static const String deleteStudentEndpoint = '/api/admin/students'; // + /{id}
  
  // Notifications endpoints
  static const String notificationsEndpoint = '/api/notifications';
  static const String sendNotificationEndpoint = '/api/admin/notifications';
  static const String markAsReadEndpoint = '/api/notifications'; // + /{id}/read
  
  // Headers
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  static Map<String, String> getAuthHeaders(String token) => {
    ...defaultHeaders,
    'Authorization': 'Bearer $token',
  };
  
  // Helper methods
  static String getStudentUrl(int? id) {
    return id != null ? '$studentsEndpoint/$id' : studentsEndpoint;
  }
  
  static String getNotificationUrl(int? id) {
    return id != null ? '$notificationsEndpoint/$id' : notificationsEndpoint;
  }
  
  // Timeout configurations
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
}
