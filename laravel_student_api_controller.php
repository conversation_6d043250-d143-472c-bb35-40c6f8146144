<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class StudentController extends Controller
{
    /**
     * Display a listing of students.
     */
    public function index()
    {
        try {
            $students = Student::orderBy('created_at', 'desc')->get();
            
            return response()->json([
                'success' => true,
                'message' => 'Students retrieved successfully',
                'students' => $students,
                'total' => $students->count()
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve students',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created student.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'nationality' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20|unique:students,phone_number',
            'class' => 'required|string|max:255',
            'parent_phone_number' => 'required|string|max:20',
            'password' => 'nullable|string|min:6'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $student = Student::create([
                'full_name' => $request->full_name,
                'nationality' => $request->nationality,
                'phone_number' => $request->phone_number,
                'class' => $request->class,
                'parent_phone_number' => $request->parent_phone_number,
                'password' => Hash::make($request->password ?? 'student123')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Student created successfully',
                'student' => $student
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified student.
     */
    public function show($id)
    {
        try {
            $student = Student::findOrFail($id);
            
            return response()->json([
                'success' => true,
                'message' => 'Student retrieved successfully',
                'student' => $student
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Student not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified student.
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'nationality' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20|unique:students,phone_number,' . $id,
            'class' => 'required|string|max:255',
            'parent_phone_number' => 'required|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $student = Student::findOrFail($id);
            
            $student->update([
                'full_name' => $request->full_name,
                'nationality' => $request->nationality,
                'phone_number' => $request->phone_number,
                'class' => $request->class,
                'parent_phone_number' => $request->parent_phone_number,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Student updated successfully',
                'student' => $student
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified student.
     */
    public function destroy($id)
    {
        try {
            $student = Student::findOrFail($id);
            $student->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Student deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete student',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search students by name, phone, or class.
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        
        try {
            $students = Student::where('full_name', 'LIKE', "%{$query}%")
                              ->orWhere('phone_number', 'LIKE', "%{$query}%")
                              ->orWhere('class', 'LIKE', "%{$query}%")
                              ->orderBy('created_at', 'desc')
                              ->get();
            
            return response()->json([
                'success' => true,
                'message' => 'Search completed successfully',
                'students' => $students,
                'total' => $students->count()
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get students statistics.
     */
    public function statistics()
    {
        try {
            $totalStudents = Student::count();
            $studentsByNationality = Student::select('nationality')
                                           ->selectRaw('count(*) as count')
                                           ->groupBy('nationality')
                                           ->get();
            $studentsByClass = Student::select('class')
                                     ->selectRaw('count(*) as count')
                                     ->groupBy('class')
                                     ->get();
            
            return response()->json([
                'success' => true,
                'message' => 'Statistics retrieved successfully',
                'statistics' => [
                    'total_students' => $totalStudents,
                    'by_nationality' => $studentsByNationality,
                    'by_class' => $studentsByClass
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
