import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  // Base URL for your Laravel API
  static const String baseUrl = 'http://localhost/notysend/api';
  
  // For Laragon, you might need to use:
  // static const String baseUrl = 'http://notysend.test/api';
  
  // Or if accessing from physical device:
  // static const String baseUrl = 'http://*************/notysend/api';

  // Get headers with authentication token
  static Future<Map<String, String>> _getHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Handle API response
  static Map<String, dynamic> _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return json.decode(response.body);
    } else {
      throw Exception('API Error: ${response.statusCode} - ${response.body}');
    }
  }

  // Authentication APIs
  static Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/login'),
        headers: await _getHeaders(),
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم',
        'error': e.toString(),
      };
    }
  }

  static Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String password,
    required String userType,
    String? phone,
    String? department,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/register'),
        headers: await _getHeaders(),
        body: json.encode({
          'name': name,
          'email': email,
          'password': password,
          'password_confirmation': password,
          'user_type': userType,
          'phone': phone,
          'department': department,
        }),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم',
        'error': e.toString(),
      };
    }
  }

  static Future<Map<String, dynamic>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/logout'),
        headers: await _getHeaders(),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تسجيل الخروج',
        'error': e.toString(),
      };
    }
  }

  // User profile APIs
  static Future<Map<String, dynamic>> updateProfile({
    String? name,
    String? phone,
    String? department,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/profile'),
        headers: await _getHeaders(),
        body: json.encode({
          if (name != null) 'name': name,
          if (phone != null) 'phone': phone,
          if (department != null) 'department': department,
        }),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تحديث البيانات',
        'error': e.toString(),
      };
    }
  }

  // Notification APIs
  static Future<Map<String, dynamic>> getNotifications() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: await _getHeaders(),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في جلب الإشعارات',
        'error': e.toString(),
      };
    }
  }

  static Future<Map<String, dynamic>> getSentNotifications() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications/sent'),
        headers: await _getHeaders(),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في جلب الإشعارات المرسلة',
        'error': e.toString(),
      };
    }
  }

  static Future<Map<String, dynamic>> sendNotification({
    required String title,
    required String message,
    required String recipientType,
    List<int>? specificRecipients,
    String? attachmentUrl,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/notifications'),
        headers: await _getHeaders(),
        body: json.encode({
          'title': title,
          'message': message,
          'type': 'general', // Default type
          'recipient_type': recipientType,
          if (specificRecipients != null) 'specific_recipients': specificRecipients,
          if (attachmentUrl != null) 'attachment_url': attachmentUrl,
        }),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في إرسال الإشعار',
        'error': e.toString(),
      };
    }
  }

  static Future<Map<String, dynamic>> markNotificationAsRead(int notificationId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/$notificationId/read'),
        headers: await _getHeaders(),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تحديث حالة الإشعار',
        'error': e.toString(),
      };
    }
  }

  static Future<Map<String, dynamic>> markAllNotificationsAsRead() async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/read-all'),
        headers: await _getHeaders(),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تحديث حالة الإشعارات',
        'error': e.toString(),
      };
    }
  }

  static Future<Map<String, dynamic>> deleteNotification(int notificationId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/notifications/$notificationId'),
        headers: await _getHeaders(),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في حذف الإشعار',
        'error': e.toString(),
      };
    }
  }

  static Future<Map<String, dynamic>> getNotificationStats() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications/stats'),
        headers: await _getHeaders(),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في جلب الإحصائيات',
        'error': e.toString(),
      };
    }
  }

  // Users API (for admin)
  static Future<Map<String, dynamic>> getUsers({String? userType}) async {
    try {
      String url = '$baseUrl/users';
      if (userType != null) {
        url += '?user_type=$userType';
      }
      
      final response = await http.get(
        Uri.parse(url),
        headers: await _getHeaders(),
      );
      
      return _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في جلب المستخدمين',
        'error': e.toString(),
      };
    }
  }
}
