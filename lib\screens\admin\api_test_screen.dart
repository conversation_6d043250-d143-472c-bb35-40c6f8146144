import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../utils/app_theme.dart';
import '../../utils/api_config.dart';

class ApiTestScreen extends StatefulWidget {
  const ApiTestScreen({super.key});

  @override
  State<ApiTestScreen> createState() => _ApiTestScreenState();
}

class _ApiTestScreenState extends State<ApiTestScreen> {
  String _testResult = '';
  bool _isLoading = false;

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _testResult = 'جاري اختبار الاتصال...';
    });

    String result = 'نتائج اختبار الاتصال:\n\n';
    result += 'عنوان الخادم: ${ApiConfig.baseUrl}\n';
    result += 'وقت الاختبار: ${DateTime.now()}\n\n';

    try {
      // Test 0: Basic connectivity
      result += '🔍 اختبار الاتصال الأساسي...\n';
      try {
        final basicResponse = await http.get(
          Uri.parse('${ApiConfig.baseUrl}'),
          headers: {'Accept': 'text/html,application/json'},
        ).timeout(const Duration(seconds: 10));

        if (basicResponse.statusCode == 200) {
          result += '✅ الخادم يعمل (${basicResponse.statusCode})\n\n';
        } else {
          result += '⚠️ الخادم يستجيب لكن برمز: ${basicResponse.statusCode}\n\n';
        }
      } catch (e) {
        result += '❌ فشل في الوصول للخادم الأساسي: $e\n\n';
      }

      // Test 1: API test endpoint
      result += '🔍 اختبار مسار API الأساسي...\n';
      try {
        final testResponse = await http.get(
          Uri.parse('${ApiConfig.baseUrl}/api/test'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ).timeout(const Duration(seconds: 10));

        if (testResponse.statusCode == 200) {
          result += '✅ اختبار API الأساسي: نجح\n';
          result += 'الاستجابة: ${testResponse.body}\n\n';
        } else {
          result += '❌ اختبار API الأساسي: فشل\n';
          result += 'رمز الخطأ: ${testResponse.statusCode}\n';
          result += 'الاستجابة: ${testResponse.body}\n\n';
        }
      } catch (e) {
        result += '❌ اختبار API الأساسي: فشل\n';
        result += 'الخطأ: $e\n\n';
      }

      // Test 2: Students endpoint
      try {
        final studentsResponse = await http.get(
          Uri.parse('${ApiConfig.baseUrl}/api/students/all'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ).timeout(const Duration(seconds: 10));

        if (studentsResponse.statusCode == 200) {
          final data = json.decode(studentsResponse.body);
          result += '✅ اختبار قاعدة بيانات الطلاب: نجح\n';
          result += 'عدد الطلاب: ${data['total'] ?? 0}\n\n';
        } else {
          result += '❌ اختبار قاعدة بيانات الطلاب: فشل\n';
          result += 'رمز الخطأ: ${studentsResponse.statusCode}\n\n';
        }
      } catch (e) {
        result += '❌ اختبار قاعدة بيانات الطلاب: فشل\n';
        result += 'الخطأ: $e\n\n';
      }

      // Test 3: Health check
      try {
        final healthResponse = await http.get(
          Uri.parse('${ApiConfig.baseUrl}/api/health'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ).timeout(const Duration(seconds: 10));

        if (healthResponse.statusCode == 200) {
          result += '✅ فحص صحة الخادم: نجح\n';
          result += 'الاستجابة: ${healthResponse.body}\n\n';
        } else {
          result += '❌ فحص صحة الخادم: فشل\n';
          result += 'رمز الخطأ: ${healthResponse.statusCode}\n\n';
        }
      } catch (e) {
        result += '❌ فحص صحة الخادم: فشل\n';
        result += 'الخطأ: $e\n\n';
      }

      result += 'عنوان الخادم المستخدم: ${ApiConfig.baseUrl}\n';
      result += 'وقت الاختبار: ${DateTime.now()}\n';

      setState(() {
        _testResult = result;
      });

    } catch (e) {
      setState(() {
        _testResult = '❌ فشل في الاتصال بالخادم:\n\n$e\n\nتأكد من:\n1. تشغيل خادم Laravel\n2. صحة عنوان الخادم: ${ApiConfig.baseUrl}\n3. اتصال الإنترنت';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('اختبار الاتصال بالخادم'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Server info card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات الخادم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryGreen,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow('عنوان الخادم:', ApiConfig.baseUrl),
                    _buildInfoRow('مسار الطلاب:', '${ApiConfig.baseUrl}/api/students/all'),
                    _buildInfoRow('مسار الاختبار:', '${ApiConfig.baseUrl}/api/test'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Test button
            ElevatedButton(
              onPressed: _isLoading ? null : _testConnection,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('جاري الاختبار...'),
                      ],
                    )
                  : const Text(
                      'اختبار الاتصال',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
            ),

            const SizedBox(height: 20),

            // Results
            if (_testResult.isNotEmpty)
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'نتائج الاختبار',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryGreen,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Text(
                              _testResult,
                              style: const TextStyle(
                                fontSize: 14,
                                fontFamily: 'monospace',
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: AppTheme.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
