<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class Student extends Authenticatable
{
    use HasA<PERSON>Tokens, HasFactory, Notifiable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'students';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'full_name',
        'nationality',
        'phone_number',
        'class',
        'parent_phone_number',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'password' => 'hashed',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the student's full name.
     *
     * @return string
     */
    public function getFullNameAttribute($value)
    {
        return $value;
    }

    /**
     * Get the student's initials.
     *
     * @return string
     */
    public function getInitialsAttribute()
    {
        $names = explode(' ', $this->full_name);
        $initials = '';
        
        foreach ($names as $name) {
            if (!empty($name)) {
                $initials .= mb_substr($name, 0, 1);
            }
        }
        
        return mb_strtoupper($initials);
    }

    /**
     * Scope a query to search students.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('full_name', 'LIKE', "%{$search}%")
                    ->orWhere('phone_number', 'LIKE', "%{$search}%")
                    ->orWhere('class', 'LIKE', "%{$search}%")
                    ->orWhere('nationality', 'LIKE', "%{$search}%");
    }

    /**
     * Scope a query to filter by nationality.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $nationality
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByNationality($query, $nationality)
    {
        return $query->where('nationality', $nationality);
    }

    /**
     * Scope a query to filter by class.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $class
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByClass($query, $class)
    {
        return $query->where('class', $class);
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'id';
    }

    /**
     * Get notifications for this student.
     */
    public function notifications()
    {
        return $this->morphMany(Notification::class, 'notifiable');
    }

    /**
     * Get unread notifications count.
     *
     * @return int
     */
    public function getUnreadNotificationsCountAttribute()
    {
        return $this->notifications()->where('is_read', false)->count();
    }

    /**
     * Mark all notifications as read.
     *
     * @return void
     */
    public function markNotificationsAsRead()
    {
        $this->notifications()->where('is_read', false)->update(['is_read' => true]);
    }

    /**
     * Check if student has unread notifications.
     *
     * @return bool
     */
    public function hasUnreadNotifications()
    {
        return $this->notifications()->where('is_read', false)->exists();
    }

    /**
     * Get student's class year.
     *
     * @return string|null
     */
    public function getClassYearAttribute()
    {
        // Extract year from class name (e.g., "BT1 كهرباء A1 (2024-2025)" -> "2024-2025")
        preg_match('/\((\d{4}-\d{4})\)/', $this->class, $matches);
        return $matches[1] ?? null;
    }

    /**
     * Get student's department from class.
     *
     * @return string|null
     */
    public function getDepartmentAttribute()
    {
        // Extract department from class name (e.g., "BT1 كهرباء A1 (2024-2025)" -> "كهرباء")
        preg_match('/\w+\s+(\w+)\s+/', $this->class, $matches);
        return $matches[1] ?? null;
    }

    /**
     * Get student's level from class.
     *
     * @return string|null
     */
    public function getLevelAttribute()
    {
        // Extract level from class name (e.g., "BT1 كهرباء A1 (2024-2025)" -> "BT1")
        preg_match('/^(\w+)/', $this->class, $matches);
        return $matches[1] ?? null;
    }
}
