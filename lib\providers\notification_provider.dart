import 'package:flutter/material.dart';
import '../models/notification_model.dart';
import '../services/api_service.dart';
import '../utils/logger.dart';

class NotificationProvider with ChangeNotifier {
  List<NotificationModel> _notifications = [];
  List<NotificationModel> _sentNotifications = [];
  bool _isLoading = false;
  String? _error;
  int _unreadCount = 0;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  List<NotificationModel> get sentNotifications => _sentNotifications;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get unreadCount => _unreadCount;

  // Get received notifications
  Future<void> getNotifications() async {
    _setLoading(true);
    _error = null;

    try {
      final response = await ApiService.getNotifications();
      
      if (response['success'] == true) {
        _notifications = (response['notifications'] as List)
            .map((notification) => NotificationModel.fromMap(notification))
            .toList();
        
        _updateUnreadCount();
        _setLoading(false);
      } else {
        _error = response['message'] ?? 'فشل في جلب الإشعارات';
        _setLoading(false);
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم';
      _setLoading(false);
    }
  }

  // Get sent notifications (for admin)
  Future<void> getSentNotifications() async {
    _setLoading(true);
    _error = null;

    try {
      final response = await ApiService.getSentNotifications();
      
      if (response['success'] == true) {
        _sentNotifications = (response['notifications'] as List)
            .map((notification) => NotificationModel.fromMap(notification))
            .toList();
        
        _setLoading(false);
      } else {
        _error = response['message'] ?? 'فشل في جلب الإشعارات المرسلة';
        _setLoading(false);
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم';
      _setLoading(false);
    }
  }

  // Send notification (admin only)
  Future<bool> sendNotification({
    required String title,
    required String message,
    required String recipientType, // 'students', 'teachers', 'all'
    List<int>? specificRecipients,
    String? attachmentUrl,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await ApiService.sendNotification(
        title: title,
        message: message,
        recipientType: recipientType,
        specificRecipients: specificRecipients,
        attachmentUrl: attachmentUrl,
      );
      
      if (response['success'] == true) {
        // Add to sent notifications list
        final newNotification = NotificationModel.fromMap(response['notification']);
        _sentNotifications.insert(0, newNotification);
        
        _setLoading(false);
        return true;
      } else {
        _error = response['message'] ?? 'فشل في إرسال الإشعار';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم';
      _setLoading(false);
      return false;
    }
  }

  // Mark notification as read
  Future<void> markAsRead(int notificationId) async {
    try {
      final response = await ApiService.markNotificationAsRead(notificationId);
      
      if (response['success'] == true) {
        // Update local notification
        final index = _notifications.indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          _notifications[index] = _notifications[index].copyWith(isRead: true);
          _updateUnreadCount();
          notifyListeners();
        }
      }
    } catch (e) {
      AppLogger.error('Error marking notification as read', e);
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final response = await ApiService.markAllNotificationsAsRead();
      
      if (response['success'] == true) {
        // Update all local notifications
        _notifications = _notifications
            .map((notification) => notification.copyWith(isRead: true))
            .toList();
        
        _updateUnreadCount();
        notifyListeners();
      }
    } catch (e) {
      AppLogger.error('Error marking all notifications as read', e);
    }
  }

  // Delete notification
  Future<bool> deleteNotification(int notificationId) async {
    try {
      final response = await ApiService.deleteNotification(notificationId);
      
      if (response['success'] == true) {
        // Remove from local list
        _notifications.removeWhere((n) => n.id == notificationId);
        _sentNotifications.removeWhere((n) => n.id == notificationId);
        
        _updateUnreadCount();
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'فشل في حذف الإشعار';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم';
      return false;
    }
  }

  // Get notification statistics (for admin)
  Future<Map<String, dynamic>?> getNotificationStats() async {
    try {
      final response = await ApiService.getNotificationStats();
      
      if (response['success'] == true) {
        return response['stats'];
      } else {
        _error = response['message'] ?? 'فشل في جلب الإحصائيات';
        return null;
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم';
      return null;
    }
  }

  // Filter notifications by type
  List<NotificationModel> getNotificationsByType(String type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Get unread notifications
  List<NotificationModel> get unreadNotifications {
    return _notifications.where((n) => !n.isRead).toList();
  }

  // Update unread count
  void _updateUnreadCount() {
    _unreadCount = _notifications.where((n) => !n.isRead).length;
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Refresh notifications
  Future<void> refreshNotifications() async {
    await getNotifications();
  }

  // Search notifications
  List<NotificationModel> searchNotifications(String query) {
    if (query.isEmpty) return _notifications;
    
    return _notifications.where((notification) {
      return notification.title.toLowerCase().contains(query.toLowerCase()) ||
             notification.message.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }
}
