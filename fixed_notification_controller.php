<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Notification;
use App\Models\Student;
use App\Models\Emp;
use App\Models\User;

class NotificationController extends Controller
{
    public function index()
    {
        // Use query builder for pagination, don't call get() before paginate()
        $notifications = Notification::with('admin')
                                   ->latest()
                                   ->paginate(10);

        return view('admin.notifications.index', compact('notifications'));
    }

    public function create()
    {
        $students = Student::all();
        $employees = Emp::all();
        
        return view('admin.notifications.create', compact('students', 'employees'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'recipient_type' => 'required|in:all,students,employees,specific',
            'student_ids' => 'nullable|array',
            'employee_ids' => 'nullable|array',
            'student_classes' => 'nullable|array',
            'employee_companies' => 'nullable|array',
        ]);

        $notification = new Notification();
        $notification->title = $request->title;
        $notification->message = $request->message;
        $notification->admin_id = auth()->id();
        $notification->recipient_type = $request->recipient_type;
        $notification->has_attachment = false; // Set to true if you handle file uploads

        // Handle specific recipients
        if ($request->recipient_type === 'specific') {
            $notification->student_ids = $request->student_ids ?? [];
            $notification->employee_ids = $request->employee_ids ?? [];
        }

        // Handle class/company based recipients
        if ($request->student_classes) {
            $notification->student_classes = $request->student_classes;
        }
        
        if ($request->employee_companies) {
            $notification->employee_companies = $request->employee_companies;
        }

        $notification->save();

        return redirect()->route('admin.notifications.index')
                        ->with('success', 'Notification sent successfully!');
    }

    public function show(Notification $notification)
    {
        $notification->load('admin');
        return view('admin.notifications.show', compact('notification'));
    }

    public function edit(Notification $notification)
    {
        $students = Student::all();
        $employees = Emp::all();
        
        return view('admin.notifications.edit', compact('notification', 'students', 'employees'));
    }

    public function update(Request $request, Notification $notification)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'recipient_type' => 'required|in:all,students,employees,specific',
            'student_ids' => 'nullable|array',
            'employee_ids' => 'nullable|array',
            'student_classes' => 'nullable|array',
            'employee_companies' => 'nullable|array',
        ]);

        $notification->title = $request->title;
        $notification->message = $request->message;
        $notification->recipient_type = $request->recipient_type;

        // Handle specific recipients
        if ($request->recipient_type === 'specific') {
            $notification->student_ids = $request->student_ids ?? [];
            $notification->employee_ids = $request->employee_ids ?? [];
        } else {
            $notification->student_ids = [];
            $notification->employee_ids = [];
        }

        // Handle class/company based recipients
        $notification->student_classes = $request->student_classes ?? [];
        $notification->employee_companies = $request->employee_companies ?? [];

        $notification->save();

        return redirect()->route('admin.notifications.index')
                        ->with('success', 'Notification updated successfully!');
    }

    public function destroy(Notification $notification)
    {
        $notification->delete();

        return redirect()->route('admin.notifications.index')
                        ->with('success', 'Notification deleted successfully!');
    }

    public function markAsRead(Request $request, Notification $notification)
    {
        // Implementation for marking notification as read
        // This depends on your notification read tracking system
        
        return response()->json(['success' => true]);
    }
}
