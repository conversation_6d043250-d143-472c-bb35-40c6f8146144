<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationAttachment extends Model
{
    use HasFactory;

    protected $table = 'notification_attachments';

    protected $fillable = [
        'notification_id',
        'file_name',
        'file_path',
        'file_size',
        'file_type',
        'original_name',
    ];

    /**
     * Get the notification that owns this attachment
     */
    public function notification()
    {
        return $this->belongsTo(Notification::class, 'notification_id');
    }

    /**
     * Get the full URL for the attachment
     */
    public function getUrlAttribute()
    {
        return asset('storage/' . $this->file_path);
    }

    /**
     * Get human readable file size
     */
    public function getFormattedSizeAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is an image
     */
    public function isImage()
    {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        $extension = strtolower(pathinfo($this->file_name, PATHINFO_EXTENSION));
        
        return in_array($extension, $imageTypes);
    }

    /**
     * Check if file is a document
     */
    public function isDocument()
    {
        $docTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
        $extension = strtolower(pathinfo($this->file_name, PATHINFO_EXTENSION));
        
        return in_array($extension, $docTypes);
    }

    /**
     * Get file icon based on type
     */
    public function getIconAttribute()
    {
        if ($this->isImage()) {
            return 'fas fa-image';
        } elseif ($this->isDocument()) {
            return 'fas fa-file-alt';
        } else {
            return 'fas fa-file';
        }
    }
}
