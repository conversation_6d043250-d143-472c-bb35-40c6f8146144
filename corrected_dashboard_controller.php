<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Student;
use App\Models\Notification;
use App\Models\User;
use App\Models\Emp;

class DashboardController extends Controller
{
    public function index()
    {
        $totalStudents = Student::count();
        $totalEmployees = Emp::count();
        $totalNotifications = Notification::count();
        $totalAdminUsers = User::count();
        
        // Get recent notifications with admin relationship
        $recentNotifications = Notification::with('admin')
                                          ->latest()
                                          ->take(5)
                                          ->get()
                                          ->map(function ($notification) {
                                              // Ensure admin name is available, use fallback if null
                                              $notification->admin_name = $notification->admin ? $notification->admin->name : 'Unknown Admin';
                                              return $notification;
                                          });

        return view('admin.dashboard', compact(
            'totalStudents', 
            'totalEmployees',
            'totalNotifications', 
            'totalAdminUsers', 
            'recentNotifications'
        ));
    }
}
