import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/notification_provider.dart';
import '../../utils/app_theme.dart';

class SendNotificationScreen extends StatefulWidget {
  const SendNotificationScreen({super.key});

  @override
  State<SendNotificationScreen> createState() => _SendNotificationScreenState();
}

class _SendNotificationScreenState extends State<SendNotificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  
  String _selectedRecipientType = 'all';
  String _selectedNotificationType = 'general';
  
  final List<Map<String, String>> _recipientTypes = [
    {'value': 'all', 'label': 'الجميع'},
    {'value': 'students', 'label': 'الطلاب فقط'},
    {'value': 'teachers', 'label': 'المعلمين فقط'},
  ];
  
  final List<Map<String, String>> _notificationTypes = [
    {'value': 'general', 'label': 'عام'},
    {'value': 'urgent', 'label': 'عاجل'},
    {'value': 'announcement', 'label': 'إعلان'},
    {'value': 'reminder', 'label': 'تذكير'},
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _sendNotification() async {
    if (!_formKey.currentState!.validate()) return;

    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
    
    final success = await notificationProvider.sendNotification(
      title: _titleController.text.trim(),
      message: _messageController.text.trim(),
      recipientType: _selectedRecipientType,
    );

    if (!mounted) return;

    if (success) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال الإشعار بنجاح'),
          backgroundColor: AppTheme.successColor,
        ),
      );
      
      // Clear form
      _titleController.clear();
      _messageController.clear();
      setState(() {
        _selectedRecipientType = 'all';
        _selectedNotificationType = 'general';
      });
    } else {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(notificationProvider.error ?? 'فشل في إرسال الإشعار'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إرسال إشعار جديد'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Instructions card
              Card(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppTheme.primaryGreen,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'يمكنك إرسال إشعارات للطلاب والمعلمين من خلال هذا النموذج',
                          style: TextStyle(
                            color: AppTheme.primaryGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Title field
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان الإشعار',
                  prefixIcon: Icon(Icons.title),
                  hintText: 'أدخل عنوان الإشعار',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال عنوان الإشعار';
                  }
                  if (value.trim().length < 3) {
                    return 'العنوان يجب أن يكون 3 أحرف على الأقل';
                  }
                  return null;
                },
                maxLength: 100,
              ),
              
              const SizedBox(height: 20),
              
              // Message field
              TextFormField(
                controller: _messageController,
                decoration: const InputDecoration(
                  labelText: 'نص الإشعار',
                  prefixIcon: Icon(Icons.message),
                  hintText: 'أدخل نص الإشعار',
                  alignLabelWithHint: true,
                ),
                maxLines: 5,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال نص الإشعار';
                  }
                  if (value.trim().length < 10) {
                    return 'نص الإشعار يجب أن يكون 10 أحرف على الأقل';
                  }
                  return null;
                },
                maxLength: 500,
              ),
              
              const SizedBox(height: 20),
              
              // Recipient type dropdown
              DropdownButtonFormField<String>(
                value: _selectedRecipientType,
                decoration: const InputDecoration(
                  labelText: 'المستقبلين',
                  prefixIcon: Icon(Icons.group),
                ),
                items: _recipientTypes.map((type) {
                  return DropdownMenuItem<String>(
                    value: type['value'],
                    child: Text(type['label']!),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedRecipientType = value!;
                  });
                },
              ),
              
              const SizedBox(height: 20),
              
              // Notification type dropdown
              DropdownButtonFormField<String>(
                value: _selectedNotificationType,
                decoration: const InputDecoration(
                  labelText: 'نوع الإشعار',
                  prefixIcon: Icon(Icons.category),
                ),
                items: _notificationTypes.map((type) {
                  return DropdownMenuItem<String>(
                    value: type['value'],
                    child: Row(
                      children: [
                        Icon(
                          _getNotificationIcon(type['value']!),
                          size: 20,
                          color: _getNotificationColor(type['value']!),
                        ),
                        const SizedBox(width: 8),
                        Text(type['label']!),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedNotificationType = value!;
                  });
                },
              ),
              
              const SizedBox(height: 30),
              
              // Preview card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.preview,
                            color: AppTheme.primaryGreen,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'معاينة الإشعار',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryGreen,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 16,
                                  backgroundColor: _getNotificationColor(_selectedNotificationType).withValues(alpha: 0.2),
                                  child: Icon(
                                    _getNotificationIcon(_selectedNotificationType),
                                    size: 16,
                                    color: _getNotificationColor(_selectedNotificationType),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _titleController.text.isEmpty 
                                        ? 'عنوان الإشعار' 
                                        : _titleController.text,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: _titleController.text.isEmpty 
                                          ? Colors.grey[500] 
                                          : AppTheme.textPrimary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: 8),
                            
                            Text(
                              _messageController.text.isEmpty 
                                  ? 'نص الإشعار سيظهر هنا...' 
                                  : _messageController.text,
                              style: TextStyle(
                                color: _messageController.text.isEmpty 
                                    ? Colors.grey[500] 
                                    : AppTheme.textSecondary,
                              ),
                            ),
                            
                            const SizedBox(height: 8),
                            
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: _getNotificationColor(_selectedNotificationType).withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    _notificationTypes.firstWhere(
                                      (type) => type['value'] == _selectedNotificationType
                                    )['label']!,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: _getNotificationColor(_selectedNotificationType),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: AppTheme.infoColor.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    _recipientTypes.firstWhere(
                                      (type) => type['value'] == _selectedRecipientType
                                    )['label']!,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.infoColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 30),
              
              // Send button
              Consumer<NotificationProvider>(
                builder: (context, notificationProvider, child) {
                  return ElevatedButton.icon(
                    onPressed: notificationProvider.isLoading ? null : _sendNotification,
                    icon: notificationProvider.isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.send),
                    label: Text(
                      notificationProvider.isLoading ? 'جاري الإرسال...' : 'إرسال الإشعار',
                      style: const TextStyle(fontSize: 16),
                    ),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'urgent':
        return AppTheme.errorColor;
      case 'announcement':
        return AppTheme.infoColor;
      case 'reminder':
        return AppTheme.warningColor;
      default:
        return AppTheme.primaryGreen;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'urgent':
        return Icons.priority_high;
      case 'announcement':
        return Icons.campaign;
      case 'reminder':
        return Icons.schedule;
      default:
        return Icons.notifications;
    }
  }
}
