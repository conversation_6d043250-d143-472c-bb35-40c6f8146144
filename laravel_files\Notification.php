<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'message',
        'type',
        'recipient_type',
        'sender_id',
        'specific_recipients',
        'is_read',
        'attachment_url',
        'attachment_name',
    ];

    protected $casts = [
        'specific_recipients' => 'array',
        'is_read' => 'boolean',
    ];

    /**
     * Get the user who sent this notification
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Scope for notifications that should be visible to a specific user
     */
    public function scopeForUser($query, User $user)
    {
        return $query->where(function ($q) use ($user) {
            $q->where('recipient_type', 'all')
              ->orWhere('recipient_type', $user->user_type . 's')
              ->orWhere(function ($subQuery) use ($user) {
                  $subQuery->where('recipient_type', 'specific')
                           ->whereJsonContains('specific_recipients', $user->id);
              });
        });
    }
}
