<?php
// أضف هذا الكود إلى ملف routes/api.php في Laravel

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;

// مسار اختبار بسيط
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working!',
        'timestamp' => now(),
        'server' => 'Laravel'
    ]);
});

// مسار الطلاب (استعلام مباشر من قاعدة البيانات)
Route::get('/students/all', function () {
    try {
        $students = DB::table('students')->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Students retrieved successfully',
            'students' => $students,
            'total' => $students->count()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to retrieve students',
            'error' => $e->getMessage()
        ], 500);
    }
});

// مسار فحص قاعدة البيانات
Route::get('/test-db', function () {
    try {
        $studentsCount = DB::table('students')->count();
        
        return response()->json([
            'success' => true,
            'message' => 'Database connection successful',
            'students_count' => $studentsCount
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ], 500);
    }
});

// مسار فحص الصحة
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now()
    ]);
});
