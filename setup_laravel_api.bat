@echo off
echo Setting up <PERSON><PERSON> API for NotiSend...

cd C:\laragon\www\notysend

echo.
echo 1. Installing Laravel Sanctum...
composer require laravel/sanctum

echo.
echo 2. Publishing Sanctum configuration...
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

echo.
echo 3. Creating controllers...
php artisan make:controller Api/AuthController
php artisan make:controller Api/NotificationController
php artisan make:controller Api/UserController

echo.
echo 4. Creating models...
php artisan make:model Notification -m

echo.
echo 5. Creating seeders...
php artisan make:seeder AdminSeeder

echo.
echo 6. Running migrations...
php artisan migrate

echo.
echo 7. Running seeders...
php artisan db:seed --class=AdminSeeder

echo.
echo Setup complete! Your Laravel API is ready.
echo.
echo Test credentials:
echo Admin: <EMAIL> / admin123
echo Teacher: <EMAIL> / teacher123
echo Student: <EMAIL> / student123
echo.
echo Laravel API URL: http://notysend.test/api
echo.
pause
