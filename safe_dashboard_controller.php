<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Student;
use App\Models\Notification;
use App\Models\User;

class DashboardController extends Controller
{
    public function index()
    {
        $totalStudents = Student::count();
        $totalNotifications = Notification::count();
        $totalAdminUsers = User::count();
        
        // Get recent notifications with sender, but handle cases where sender might be null
        $recentNotifications = Notification::with('sender')
                                          ->latest()
                                          ->take(5)
                                          ->get()
                                          ->map(function ($notification) {
                                              // Ensure sender name is available, use fallback if null
                                              $notification->sender_name = $notification->sender ? $notification->sender->name : 'Unknown Sender';
                                              return $notification;
                                          });

        return view('admin.dashboard', compact('totalStudents', 'totalNotifications', 'totalAdminUsers', 'recentNotifications'));
    }
}
