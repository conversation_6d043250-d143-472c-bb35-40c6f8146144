import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/student_model.dart';
import '../utils/api_config.dart';

class StudentProvider with ChangeNotifier {
  List<Student> _students = [];
  bool _isLoading = false;
  String? _error;

  List<Student> get students => _students;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Add a new student
  Future<bool> addStudent(Student student) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/admin/students'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          // Add authorization header if needed
          // 'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'full_name': student.fullName,
          'nationality': student.nationality,
          'phone_number': student.phoneNumber,
          'class': student.studentClass,
          'parent_phone_number': student.parentPhoneNumber,
          'password': student.password ?? 'student123', // Default password
        }),
      );

      if (response.statusCode == 201) {
        final responseData = json.decode(response.body);
        final newStudent = Student.fromMap(responseData['student']);
        _students.insert(0, newStudent);
        notifyListeners();
        return true;
      } else {
        final responseData = json.decode(response.body);
        _error = responseData['message'] ?? 'فشل في إضافة الطالب';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get all students
  Future<void> getStudents() async {
    _setLoading(true);
    _error = null;

    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/admin/students'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          // Add authorization header if needed
          // 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final List<dynamic> studentsData = responseData['students'];
        _students = studentsData.map((data) => Student.fromMap(data)).toList();
        notifyListeners();
      } else {
        _error = 'فشل في تحميل قائمة الطلاب';
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Update student
  Future<bool> updateStudent(Student student) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await http.put(
        Uri.parse('${ApiConfig.baseUrl}/api/admin/students/${student.id}'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          // Add authorization header if needed
          // 'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'full_name': student.fullName,
          'nationality': student.nationality,
          'phone_number': student.phoneNumber,
          'class': student.studentClass,
          'parent_phone_number': student.parentPhoneNumber,
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final updatedStudent = Student.fromMap(responseData['student']);
        
        final index = _students.indexWhere((s) => s.id == student.id);
        if (index != -1) {
          _students[index] = updatedStudent;
          notifyListeners();
        }
        return true;
      } else {
        final responseData = json.decode(response.body);
        _error = responseData['message'] ?? 'فشل في تحديث بيانات الطالب';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete student
  Future<bool> deleteStudent(int studentId) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await http.delete(
        Uri.parse('${ApiConfig.baseUrl}/api/admin/students/$studentId'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          // Add authorization header if needed
          // 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        _students.removeWhere((student) => student.id == studentId);
        notifyListeners();
        return true;
      } else {
        final responseData = json.decode(response.body);
        _error = responseData['message'] ?? 'فشل في حذف الطالب';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Search students
  List<Student> searchStudents(String query) {
    if (query.isEmpty) return _students;
    
    return _students.where((student) {
      return student.fullName.toLowerCase().contains(query.toLowerCase()) ||
             student.phoneNumber.contains(query) ||
             student.studentClass.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Refresh students
  Future<void> refreshStudents() async {
    await getStudents();
  }
}
