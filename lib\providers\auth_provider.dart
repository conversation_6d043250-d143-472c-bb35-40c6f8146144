import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String? _error;
  bool _isLoggedIn = false;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _isLoggedIn;

  // Constructor
  AuthProvider() {
    _checkLoginStatus();
  }

  // Check if user is already logged in
  Future<void> _checkLoginStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      final userJson = prefs.getString('user_data');
      
      if (token != null && userJson != null) {
        _user = User.fromJson(userJson);
        _isLoggedIn = true;
        notifyListeners();
      }
    } catch (e) {
      print('Error checking login status: $e');
    }
  }

  // Login method
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await ApiService.login(email, password);
      
      if (response['success'] == true) {
        _user = User.fromMap(response['user']);
        _isLoggedIn = true;
        
        // Save to local storage
        await _saveUserData(response['token'], response['user']);
        
        _setLoading(false);
        return true;
      } else {
        _error = response['message'] ?? 'فشل في تسجيل الدخول';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم';
      _setLoading(false);
      return false;
    }
  }

  // Register method
  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String userType,
    String? phone,
    String? department,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await ApiService.register(
        name: name,
        email: email,
        password: password,
        userType: userType,
        phone: phone,
        department: department,
      );
      
      if (response['success'] == true) {
        _user = User.fromMap(response['user']);
        _isLoggedIn = true;
        
        // Save to local storage
        await _saveUserData(response['token'], response['user']);
        
        _setLoading(false);
        return true;
      } else {
        _error = response['message'] ?? 'فشل في إنشاء الحساب';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم';
      _setLoading(false);
      return false;
    }
  }

  // Logout method
  Future<void> logout() async {
    try {
      await ApiService.logout();
    } catch (e) {
      print('Error during logout API call: $e');
    }
    
    // Clear local data
    await _clearUserData();
    
    _user = null;
    _isLoggedIn = false;
    _error = null;
    
    notifyListeners();
  }

  // Update user profile
  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? department,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await ApiService.updateProfile(
        name: name,
        phone: phone,
        department: department,
      );
      
      if (response['success'] == true) {
        _user = User.fromMap(response['user']);
        
        // Update local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_data', _user!.toJson());
        
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'فشل في تحديث البيانات';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _error = 'خطأ في الاتصال بالخادم';
      _setLoading(false);
      return false;
    }
  }

  // Save user data to local storage
  Future<void> _saveUserData(String token, Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
    await prefs.setString('user_data', User.fromMap(userData).toJson());
  }

  // Clear user data from local storage
  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    await prefs.remove('user_data');
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Get auth token
  Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }
}
