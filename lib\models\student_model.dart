import 'dart:convert';

class Student {
  final int? id;
  final String fullName;
  final String nationality;
  final String phoneNumber;
  final String studentClass;
  final String parentPhoneNumber;
  final String? password;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Student({
    this.id,
    required this.fullName,
    required this.nationality,
    required this.phoneNumber,
    required this.studentClass,
    required this.parentPhoneNumber,
    this.password,
    this.createdAt,
    this.updatedAt,
  });

  // Factory constructor from Map
  factory Student.fromMap(Map<String, dynamic> map) {
    return Student(
      id: map['id']?.toInt(),
      fullName: map['full_name'] ?? '',
      nationality: map['nationality'] ?? '',
      phoneNumber: map['phone_number'] ?? '',
      studentClass: map['class'] ?? '',
      parentPhoneNumber: map['parent_phone_number'] ?? '',
      password: map['password'],
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at']) 
          : null,
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at']) 
          : null,
    );
  }

  // Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'full_name': fullName,
      'nationality': nationality,
      'phone_number': phoneNumber,
      'class': studentClass,
      'parent_phone_number': parentPhoneNumber,
      'password': password,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Convert to JSON string
  String toJson() => json.encode(toMap());

  // Factory constructor from JSON string
  factory Student.fromJson(String source) => Student.fromMap(json.decode(source));

  // Copy with method for updating fields
  Student copyWith({
    int? id,
    String? fullName,
    String? nationality,
    String? phoneNumber,
    String? studentClass,
    String? parentPhoneNumber,
    String? password,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Student(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      nationality: nationality ?? this.nationality,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      studentClass: studentClass ?? this.studentClass,
      parentPhoneNumber: parentPhoneNumber ?? this.parentPhoneNumber,
      password: password ?? this.password,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Student(id: $id, fullName: $fullName, nationality: $nationality, phoneNumber: $phoneNumber, studentClass: $studentClass, parentPhoneNumber: $parentPhoneNumber, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is Student &&
      other.id == id &&
      other.fullName == fullName &&
      other.nationality == nationality &&
      other.phoneNumber == phoneNumber &&
      other.studentClass == studentClass &&
      other.parentPhoneNumber == parentPhoneNumber;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      fullName.hashCode ^
      nationality.hashCode ^
      phoneNumber.hashCode ^
      studentClass.hashCode ^
      parentPhoneNumber.hashCode;
  }
}
