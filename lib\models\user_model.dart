import 'dart:convert';

class User {
  final int id;
  final String name;
  final String email;
  final String userType; // 'admin', 'teacher', 'student'
  final String? phone;
  final String? department;
  final String? profileImage;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.userType,
    this.phone,
    this.department,
    this.profileImage,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  // Factory constructor from Map
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      userType: map['user_type'] ?? map['userType'] ?? 'admin', // Default to admin for now
      phone: map['phone'],
      department: map['department'],
      profileImage: map['profile_image'] ?? map['profileImage'],
      createdAt: DateTime.parse(map['created_at'] ?? map['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? map['updatedAt'] ?? DateTime.now().toIso8601String()),
      isActive: map['is_active'] ?? map['isActive'] ?? true,
    );
  }

  // Factory constructor from JSON string
  factory User.fromJson(String source) => User.fromMap(json.decode(source));

  // Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'user_type': userType,
      'phone': phone,
      'department': department,
      'profile_image': profileImage,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
    };
  }

  // Convert to JSON string
  String toJson() => json.encode(toMap());

  // Copy with method
  User copyWith({
    int? id,
    String? name,
    String? email,
    String? userType,
    String? phone,
    String? department,
    String? profileImage,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      userType: userType ?? this.userType,
      phone: phone ?? this.phone,
      department: department ?? this.department,
      profileImage: profileImage ?? this.profileImage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  // Get user type in Arabic
  String get userTypeArabic {
    switch (userType) {
      case 'admin':
        return 'مدير';
      case 'teacher':
        return 'معلم';
      case 'student':
        return 'طالب';
      default:
        return 'مستخدم';
    }
  }

  // Get user initials for avatar
  String get initials {
    List<String> nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    } else if (nameParts.isNotEmpty) {
      return nameParts[0][0].toUpperCase();
    }
    return 'U';
  }

  // Check if user is admin
  bool get isAdmin => userType == 'admin';

  // Check if user is teacher
  bool get isTeacher => userType == 'teacher';

  // Check if user is student
  bool get isStudent => userType == 'student';

  // Get display name (name or email if name is empty)
  String get displayName => name.isNotEmpty ? name : email;

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, userType: $userType, phone: $phone, department: $department, profileImage: $profileImage, createdAt: $createdAt, updatedAt: $updatedAt, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is User &&
      other.id == id &&
      other.name == name &&
      other.email == email &&
      other.userType == userType &&
      other.phone == phone &&
      other.department == department &&
      other.profileImage == profileImage &&
      other.createdAt == createdAt &&
      other.updatedAt == updatedAt &&
      other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      name.hashCode ^
      email.hashCode ^
      userType.hashCode ^
      phone.hashCode ^
      department.hashCode ^
      profileImage.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      isActive.hashCode;
  }
}
