# دليل استكشاف أخطاء الاتصال - NotiSend

## 🚨 المشكلة: "فشل في تحميل قائمة الطلاب"

### 📋 الخطوات المطلوبة لحل المشكلة:

## **1. إ<PERSON><PERSON><PERSON> Laravel Backend**

### أ) إنشاء Student Controller
انسخ محتوى ملف `laravel_student_api_controller.php` إلى:
```
C:\laragon\www\notysend\app\Http\Controllers\Api\StudentController.php
```

### ب) إنشاء Student Model
انسخ محتوى ملف `laravel_student_model.php` إلى:
```
C:\laragon\www\notysend\app\Models\Student.php
```

### ج) تحديث API Routes
انسخ محتوى ملف `laravel_api_routes.php` إلى:
```
C:\laragon\www\notysend\routes\api.php
```

## **2. تشغيل خادم <PERSON>**

### أ) فتح Terminal في مجلد Laravel:
```bash
cd C:\laragon\www\notysend
```

### ب) تشغيل الخادم:
```bash
php artisan serve
```
أو استخدم Laragon لتشغيل Apache

### ج) التأكد من الوصول:
افتح المتصفح واذهب إلى:
```
http://notysend.test/api/test
```
يجب أن ترى رسالة نجاح

## **3. اختبار الاتصال من Flutter**

### أ) في التطبيق:
1. سجل دخول كمدير
2. اذهب إلى "اختبار الاتصال بالخادم"
3. اضغط "اختبار الاتصال"

### ب) النتائج المتوقعة:
- ✅ اختبار API الأساسي: نجح
- ✅ اختبار قاعدة بيانات الطلاب: نجح
- ✅ فحص صحة الخادم: نجح

## **4. حل المشاكل الشائعة**

### مشكلة 1: "Connection refused"
**السبب:** خادم Laravel غير مشغل
**الحل:**
```bash
cd C:\laragon\www\notysend
php artisan serve
```

### مشكلة 2: "404 Not Found"
**السبب:** المسارات غير موجودة
**الحل:** تأكد من نسخ ملف `api.php` بشكل صحيح

### مشكلة 3: "500 Internal Server Error"
**السبب:** خطأ في الكود أو قاعدة البيانات
**الحل:** 
1. تحقق من ملفات Laravel logs:
```
C:\laragon\www\notysend\storage\logs\laravel.log
```
2. تأكد من اتصال قاعدة البيانات في `.env`

### مشكلة 4: "CORS Error"
**السبب:** مشكلة في CORS
**الحل:** أضف إلى `config/cors.php`:
```php
'paths' => ['api/*'],
'allowed_methods' => ['*'],
'allowed_origins' => ['*'],
'allowed_headers' => ['*'],
```

## **5. التحقق من إعدادات قاعدة البيانات**

### أ) ملف .env في Laravel:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=notysend
DB_USERNAME=root
DB_PASSWORD=
```

### ب) تشغيل Migration (إذا لزم الأمر):
```bash
php artisan migrate
```

## **6. اختبار مباشر للـ API**

### أ) اختبار API الأساسي:
```
GET http://notysend.test/api/test
```

### ب) اختبار قائمة الطلاب:
```
GET http://notysend.test/api/students/all
```

### ج) اختبار صحة الخادم:
```
GET http://notysend.test/api/health
```

## **7. تحديث عنوان الخادم في Flutter**

إذا كان خادم Laravel يعمل على عنوان مختلف، حدث:
```dart
// في lib/utils/api_config.dart
static const String baseUrl = 'http://localhost:8000'; // أو العنوان الصحيح
```

## **8. خطوات التشخيص المتقدم**

### أ) فحص الشبكة:
```bash
ping notysend.test
```

### ب) فحص المنافذ:
```bash
netstat -an | findstr :80
```

### ج) فحص Apache/Nginx:
تأكد من تشغيل خادم الويب في Laragon

## **9. رسائل الخطأ الشائعة وحلولها**

| رسالة الخطأ | السبب المحتمل | الحل |
|-------------|---------------|------|
| Connection timeout | خادم Laravel متوقف | تشغيل `php artisan serve` |
| 404 Not Found | مسار API خاطئ | تحقق من routes/api.php |
| 500 Server Error | خطأ في الكود | تحقق من Laravel logs |
| CORS Error | مشكلة CORS | تحديث إعدادات CORS |
| Database Error | مشكلة قاعدة البيانات | تحقق من .env و migration |

## **10. الاختبار النهائي**

بعد تطبيق جميع الخطوات:
1. شغل خادم Laravel
2. افتح التطبيق
3. سجل دخول كمدير
4. اذهب إلى "اختبار الاتصال بالخادم"
5. اضغط "اختبار الاتصال"
6. تأكد من نجاح جميع الاختبارات
7. جرب "إدارة الطلاب"

## **📞 إذا استمرت المشكلة:**

1. تأكد من تشغيل Laragon
2. تحقق من عمل قاعدة البيانات
3. راجع ملفات Laravel logs
4. استخدم صفحة "اختبار الاتصال" للتشخيص
5. تأكد من صحة عنوان الخادم في Flutter

**بعد تطبيق هذه الخطوات، يجب أن تعمل ميزة إدارة الطلاب بشكل طبيعي! ✅**
